# 🚀 Execute Deployment - Direct Droplet Access Required

**Status:** Ready for immediate execution  
**Method:** Direct droplet access via SSH or Digital Ocean Console  
**Estimated Time:** 15-30 minutes  

---

## 🎯 **Quick Deployment Options**

### **Option 1: SSH Access (Recommended)**
If you have SSH access configured:

```bash
# 1. Transfer the deployment script
scp deploy-to-droplet.sh root@***************:/tmp/

# 2. SSH to droplet and execute
ssh root@***************
chmod +x /tmp/deploy-to-droplet.sh
/tmp/deploy-to-droplet.sh
```

### **Option 2: Digital Ocean Console**
1. Go to https://cloud.digitalocean.com/droplets/*********/console
2. Login to your Digital Ocean account
3. Access the droplet console
4. Copy and paste the deployment commands below

### **Option 3: Manual File Transfer**
1. Use SFTP/SCP to transfer files to droplet
2. Execute the deployment script manually

---

## 🔧 **Direct Console Commands**

If using Digital Ocean console, execute these commands step by step:

### **Step 1: Setup Environment**
```bash
# Create deployment directory
mkdir -p /opt/sanad/storage /opt/sanad/logs /opt/sanad/traefik
cd /opt/sanad

# Create environment file
cat > .env << 'EOF'
# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=94c69abeac98621da9b803a15893ea2c
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Security Configuration
JWT_SECRET=7f3e9c4a8d2b1f6e5c9a3d7b2e8f4c1a9e6d3b7f2c5a8d1e4b9c6f3a7d2e5b8c1f4a9e6d3b7
ENCRYPTION_KEY=9a2c7f1e4b8d5c3a6f9e2d7b4c8a1f5e3d6b9c2a7f4e1d8b5c3a9f6e2d7b4c1a8f5e3d6b9c2

# Email Configuration
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ADMIN_API_KEY=admin-key-change-me

# Appwrite Configuration
APPWRITE_PROJECT_ID=68613111000daf6c1e1f
APPWRITE_API_KEY=standard_85ba900e4f4afe4a1e4b89d23222addd4fe187b22cb7527e473be03d39ed6b32ddd49eea4fb4596da8f8962376534ec15078d6933732b5acef07c5360d4e99a6cb006f85830182a999981892fd90fe9022cceeb62d37702ad7d7b3b47100ff4a4c4ede3846cd972fdc263de8009bd0e019fea5bcc2fc958621f8dec77f892ff1
APPWRITE_DATABASE_ID=68613111000daf6c1e1f
APPWRITE_STORAGE_BUCKET_ID=default

# Database Configuration
_APP_DB_USER=appwrite
_APP_DB_PASS=secure_db_password_2024
_APP_DB_ROOT_PASS=secure_root_password_2024

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_2024

# Appwrite Internal Configuration
_APP_OPENSSL_KEY_V1=your_openssl_key_here_32_chars_min
EOF
```

### **Step 2: Create Docker Compose Configuration**
```bash
# Create the unified Docker Compose file
cat > docker-compose.unified.yml << 'EOF'
version: '3.8'

services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v2.10
    container_name: sanad-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik
      - appwrite-certificates:/certificates
    networks:
      - appwrite
      - sanad-network
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.dns.acme.email=<EMAIL>
      - --certificatesresolvers.dns.acme.storage=/certificates/acme.json
      - --certificatesresolvers.dns.acme.httpchallenge.entrypoint=web

  # PAIM Node.js Application (placeholder)
  sanad-app:
    image: node:18-alpine
    container_name: sanad-paim-app
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm init -y && npm install express && echo 'const express = require(\"express\"); const app = express(); app.get(\"/api/v1/health\", (req, res) => res.json({status: \"ok\", timestamp: new Date().toISOString(), service: \"paim-placeholder\"})); app.listen(3000, () => console.log(\"PAIM placeholder running on port 3000\"));' > server.js && node server.js"
    networks:
      - appwrite
      - sanad-network
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.sanad_api.rule=Host(\`api.sanad.kanousai.com\`) || Host(\`sanad.kanousai.com\`)"
      - "traefik.http.routers.sanad_api.service=sanad_api"
      - "traefik.http.routers.sanad_api.tls=true"
      - "traefik.http.routers.sanad_api.tls.certresolver=dns"
      - "traefik.http.services.sanad_api.loadbalancer.server.port=3000"

networks:
  appwrite:
    external: true
  sanad-network:
    name: sanad-network

volumes:
  appwrite-certificates:
    external: true
EOF
```

### **Step 3: Deploy the Application**
```bash
# Stop existing services
docker-compose down 2>/dev/null || true

# Start the unified stack
docker-compose -f docker-compose.unified.yml up -d

# Wait for services to start
sleep 60

# Check service status
docker-compose -f docker-compose.unified.yml ps

# Test health endpoints
curl -f http://localhost:3000/api/v1/health

# Show running containers
docker ps

# Check logs
docker-compose -f docker-compose.unified.yml logs --tail=20
```

---

## 🌐 **Configure DNS (After Deployment)**

Add this DNS record in your DNS provider:
- **Type:** A
- **Name:** api.sanad.kanousai.com
- **Value:** ***************
- **TTL:** 300

---

## ✅ **Verification Steps**

After deployment, verify:

```bash
# 1. Check all containers are running
docker ps

# 2. Test internal health endpoint
curl http://localhost:3000/api/v1/health

# 3. Check Appwrite is still accessible
curl http://appwrite.sanad.kanousai.com/health

# 4. Test Traefik dashboard
curl http://localhost:8080/api/rawdata

# 5. Check logs for any errors
docker logs sanad-paim-app
docker logs sanad-traefik
```

---

## 🎯 **Expected Results**

After successful deployment:
- ✅ PAIM placeholder app running on port 3000
- ✅ Traefik routing configured
- ✅ Health endpoint responding
- ✅ Ready for actual application deployment

**Next Steps:**
1. Configure DNS record
2. Test SSL certificate generation
3. Deploy actual PAIM application code
4. Verify all functionality

---

## 🚨 **If You Need Help**

If you encounter issues:
1. Check container logs: `docker logs <container-name>`
2. Verify network connectivity: `docker network ls`
3. Test internal services: `curl http://localhost:3000/api/v1/health`
4. Restart services: `docker-compose restart`

**The deployment is ready for immediate execution!**
