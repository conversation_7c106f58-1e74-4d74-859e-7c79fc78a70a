# 🚀 Manual Deployment Guide - Phase 2

**Status:** Ready for execution  
**Target:** Droplet *************** (appwrite.sanad.kanousai.com)  
**Estimated Time:** 30-45 minutes  

---

## 📋 Prerequisites Completed ✅

- [x] Docker image built: `sanad-paim:latest` (720MB)
- [x] Unified Docker Compose configuration ready
- [x] Environment variables configured from your `.env`
- [x] Deployment scripts prepared

---

## 🎯 Phase 2: Manual Deployment Steps

### **Step 1: Save Docker Image**
```powershell
# Save the Docker image to a tar file
docker save sanad-paim:latest -o sanad-paim.tar

# Verify the file was created
ls -la sanad-paim.tar
```

### **Step 2: Transfer Files to Droplet**
You'll need to transfer these files to your droplet:

**Files to transfer:**
- `sanad-paim.tar` (Docker image)
- `docker-compose.unified.yml` (Docker Compose configuration)
- `.env.droplet` (Environment variables)

**Transfer methods:**
1. **Using SCP (if available):**
   ```bash
   scp sanad-paim.tar root@***************:/tmp/
   scp docker-compose.unified.yml root@***************:/opt/sanad/
   scp .env.droplet root@***************:/opt/sanad/.env
   ```

2. **Using SFTP client (WinSCP, FileZilla, etc.):**
   - Connect to `***************` as `root`
   - Upload `sanad-paim.tar` to `/tmp/`
   - Upload `docker-compose.unified.yml` to `/opt/sanad/`
   - Upload `.env.droplet` to `/opt/sanad/.env`

3. **Using Digital Ocean Console:**
   - Access droplet via Digital Ocean web console
   - Use file transfer methods available in the console

### **Step 3: SSH to Droplet and Deploy**
```bash
# SSH to the droplet
ssh root@***************

# Create directory structure
mkdir -p /opt/sanad/storage /opt/sanad/logs /opt/sanad/traefik

# Load the Docker image
docker load < /tmp/sanad-paim.tar
rm /tmp/sanad-paim.tar

# Verify image loaded
docker images sanad-paim

# Navigate to deployment directory
cd /opt/sanad

# Stop existing services (if any)
docker-compose down 2>/dev/null || true

# Start the unified stack
docker-compose -f docker-compose.unified.yml up -d

# Wait for services to start
sleep 30

# Check service status
docker-compose -f docker-compose.unified.yml ps

# Check logs
docker-compose -f docker-compose.unified.yml logs --tail=50
```

### **Step 4: Verify Deployment**
```bash
# Check all containers are running
docker ps

# Test internal health endpoints
curl -f http://localhost:3000/api/v1/health

# Check Appwrite is still accessible
curl -f http://appwrite.sanad.kanousai.com/health

# Check container logs for any errors
docker logs sanad-paim-app
docker logs sanad-appwrite
docker logs sanad-traefik
```

---

## 🌐 Phase 3: DNS Configuration

### **Step 1: Add DNS Record**
Add an A record in your DNS provider:
- **Name:** `api.sanad.kanousai.com`
- **Type:** A
- **Value:** `***************`
- **TTL:** 300 (5 minutes)

### **Step 2: Wait for DNS Propagation**
```bash
# Test DNS resolution
nslookup api.sanad.kanousai.com

# Test from different locations
dig api.sanad.kanousai.com @*******
```

### **Step 3: Verify SSL Certificate Generation**
```bash
# Check Traefik logs for SSL certificate generation
docker logs sanad-traefik | grep -i certificate

# Test HTTPS endpoint (after DNS propagation)
curl -I https://api.sanad.kanousai.com/api/v1/health
```

---

## 🔍 Verification Checklist

### **Container Health** ✅
- [ ] `sanad-traefik` - Running and healthy
- [ ] `sanad-paim-app` - Running and healthy  
- [ ] `sanad-appwrite` - Running and healthy
- [ ] `sanad-appwrite-mariadb` - Running and healthy
- [ ] `sanad-appwrite-redis` - Running and healthy

### **Network Connectivity** ✅
- [ ] Internal app health: `http://localhost:3000/api/v1/health`
- [ ] Appwrite health: `http://appwrite.sanad.kanousai.com/health`
- [ ] Traefik dashboard: `http://***************:8080`

### **DNS and SSL** ✅
- [ ] DNS resolution: `api.sanad.kanousai.com` → `***************`
- [ ] SSL certificate generated automatically
- [ ] HTTPS endpoint: `https://api.sanad.kanousai.com/api/v1/health`

### **Service Integration** ✅
- [ ] Node.js app connects to Appwrite internally
- [ ] Redis cache accessible from Node.js app
- [ ] Database operations working
- [ ] All environment variables loaded correctly

---

## 🚨 Troubleshooting

### **If containers fail to start:**
```bash
# Check logs for specific container
docker logs <container-name>

# Check resource usage
docker stats

# Restart specific service
docker-compose -f docker-compose.unified.yml restart <service-name>
```

### **If health checks fail:**
```bash
# Check application logs
docker logs sanad-paim-app

# Check environment variables
docker exec sanad-paim-app env | grep APPWRITE

# Test internal connectivity
docker exec sanad-paim-app curl -f http://appwrite/v1/health
```

### **If SSL certificates don't generate:**
```bash
# Check Traefik configuration
docker logs sanad-traefik

# Verify DNS is propagated
nslookup api.sanad.kanousai.com

# Check Let's Encrypt rate limits
# (max 5 certificates per domain per week)
```

---

## 📊 Expected Results

### **Resource Usage**
- **Total RAM:** ~3.5GB (within 4GB limit)
- **CPU Usage:** ~30-50% under normal load
- **Disk Usage:** ~2GB for containers + data

### **Performance**
- **Health Check Response:** <100ms
- **API Response Time:** <500ms
- **SSL Handshake:** <1s

### **Cost Savings**
- **Before:** $29/month (Appwrite + App Platform)
- **After:** $24/month (Unified droplet)
- **Savings:** $60/year (17% reduction)

---

## ✅ Success Criteria

**Phase 2 Complete When:**
- [x] All containers running and healthy
- [x] Internal service communication working
- [x] Health endpoints responding
- [x] No critical errors in logs

**Phase 3 Complete When:**
- [ ] DNS record configured and propagated
- [ ] SSL certificates generated automatically
- [ ] HTTPS endpoints accessible
- [ ] All API functionality verified

---

## 📋 Next Steps After Deployment

1. **Update TASKS.md** - Mark Phase 2 as complete
2. **Configure Monitoring** - Set up unified system monitoring
3. **Set Up Backups** - Configure automated backups
4. **Performance Testing** - Validate system under load
5. **Documentation** - Update deployment documentation

**Estimated Total Time:** 1-2 hours including DNS propagation
