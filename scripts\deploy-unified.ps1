# Unified Droplet Deployment Script (PowerShell)
# Deploys PAIM application to existing Appwrite droplet

param(
    [string]$DropletIP = "***************",
    [string]$DropletUser = "root",
    [string]$AppName = "sanad-paim",
    [string]$ComposeFile = "docker-compose.unified.yml"
)

# Configuration
$ErrorActionPreference = "Stop"

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if Docker is running
    try {
        docker version | Out-Null
        Write-Success "Docker is running"
    }
    catch {
        Write-Error "Docker is not running or not installed"
        exit 1
    }
    
    # Check if Docker Compose file exists
    if (-not (Test-Path $ComposeFile)) {
        Write-Error "Docker Compose file '$ComposeFile' not found"
        exit 1
    }
    
    # Check if environment file exists
    if (-not (Test-Path ".env.droplet")) {
        Write-Error "Environment file '.env.droplet' not found"
        exit 1
    }
    
    # Check if image exists
    $imageExists = docker images $AppName --format "{{.Repository}}" | Select-String $AppName
    if (-not $imageExists) {
        Write-Error "Docker image '$AppName:latest' not found. Please build it first."
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

# Save and transfer Docker image
function Transfer-DockerImage {
    Write-Info "Saving and transferring Docker image..."
    
    try {
        # Save image to tar file (without compression for Windows compatibility)
        Write-Info "Saving Docker image to tar file..."
        docker save "${AppName}:latest" -o "${AppName}.tar"
        
        # Transfer to droplet using SCP
        Write-Info "Transferring image to droplet..."
        scp "${AppName}.tar" "${DropletUser}@${DropletIP}:/tmp/"

        # Load image on droplet
        Write-Info "Loading image on droplet..."
        ssh "${DropletUser}@${DropletIP}" "docker load < /tmp/${AppName}.tar && rm /tmp/${AppName}.tar"

        # Clean up local tar file
        Remove-Item "${AppName}.tar" -Force
        
        Write-Success "Docker image transferred successfully"
    }
    catch {
        Write-Error "Failed to transfer Docker image: $_"
        exit 1
    }
}

# Transfer configuration files
function Transfer-Configurations {
    Write-Info "Transferring configuration files..."
    
    try {
        # Create remote directory
        ssh "${DropletUser}@${DropletIP}" "mkdir -p /opt/sanad"
        
        # Transfer Docker Compose file
        scp $ComposeFile "${DropletUser}@${DropletIP}:/opt/sanad/"
        
        # Transfer environment file
        scp .env.droplet "${DropletUser}@${DropletIP}:/opt/sanad/.env"
        
        # Create necessary directories
        ssh "${DropletUser}@${DropletIP}" "mkdir -p /opt/sanad/storage /opt/sanad/logs /opt/sanad/traefik"
        
        Write-Success "Configuration files transferred successfully"
    }
    catch {
        Write-Error "Failed to transfer configuration files: $_"
        exit 1
    }
}

# Deploy application
function Deploy-Application {
    Write-Info "Deploying application on droplet..."
    
    try {
        # SSH to droplet and deploy
        $deployScript = @"
cd /opt/sanad

# Stop existing services if running
if [ -f docker-compose.yml ]; then
    docker-compose down
fi

# Start unified stack
docker-compose -f docker-compose.unified.yml up -d

# Wait for services to start
sleep 30

# Check service status
docker-compose -f docker-compose.unified.yml ps
"@
        
        ssh "${DropletUser}@${DropletIP}" $deployScript
        
        Write-Success "Application deployed successfully"
    }
    catch {
        Write-Error "Failed to deploy application: $_"
        exit 1
    }
}

# Verify deployment
function Test-Deployment {
    Write-Info "Verifying deployment..."
    
    try {
        # Wait for services to be ready
        Start-Sleep -Seconds 60
        
        # Test health endpoints
        Write-Info "Testing health endpoints..."
        
        # Test Appwrite
        try {
            $response = Invoke-WebRequest -Uri "http://appwrite.sanad.kanousai.com/health" -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Success "Appwrite health check passed"
            }
        }
        catch {
            Write-Warning "Appwrite health check failed: $_"
        }
        
        # Test Node.js app (internal)
        try {
            ssh "${DropletUser}@${DropletIP}" "curl -f -s http://localhost:3000/api/v1/health"
            Write-Success "Node.js application health check passed"
        }
        catch {
            Write-Warning "Node.js application health check failed: $_"
        }
        
        # Show running containers
        Write-Info "Running containers:"
        ssh "${DropletUser}@${DropletIP}" "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"
        
        Write-Success "Deployment verification completed"
    }
    catch {
        Write-Warning "Some verification steps failed: $_"
    }
}

# Main deployment process
function Start-Deployment {
    Write-Info "Starting unified droplet deployment..."
    
    Test-Prerequisites
    Transfer-DockerImage
    Transfer-Configurations
    Deploy-Application
    Test-Deployment
    
    Write-Success "Deployment completed successfully!"
    Write-Info "Next steps:"
    Write-Host "1. Configure DNS for api.sanad.kanousai.com -> $DropletIP" -ForegroundColor Cyan
    Write-Host "2. Test SSL certificate generation" -ForegroundColor Cyan
    Write-Host "3. Verify all API endpoints" -ForegroundColor Cyan
    Write-Host "4. Set up monitoring and backups" -ForegroundColor Cyan
}

# Run main function
try {
    Start-Deployment
}
catch {
    Write-Error "Deployment failed: $_"
    exit 1
}
