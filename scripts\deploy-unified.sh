#!/bin/bash

# Unified Droplet Deployment Script
# Deploys PAIM application to existing Appwrite droplet

set -e

# Configuration
DROPLET_IP="***************"
DROPLET_USER="root"
APP_NAME="sanad-paim"
COMPOSE_FILE="docker-compose.unified.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if SSH key exists
    if [ ! -f ~/.ssh/id_rsa ]; then
        log_error "SSH key not found. Please set up SSH access to the droplet."
        exit 1
    fi
    
    # Check if Docker Compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose file '$COMPOSE_FILE' not found."
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f ".env.droplet" ]; then
        log_error "Environment file '.env.droplet' not found."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build Docker image locally
build_image() {
    log_info "Building Docker image locally..."
    
    docker build -f Dockerfile.production -t $APP_NAME:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Save and transfer Docker image
transfer_image() {
    log_info "Saving and transferring Docker image..."
    
    # Save image to tar file
    docker save $APP_NAME:latest | gzip > ${APP_NAME}.tar.gz
    
    # Transfer to droplet
    scp ${APP_NAME}.tar.gz $DROPLET_USER@$DROPLET_IP:/tmp/
    
    # Load image on droplet
    ssh $DROPLET_USER@$DROPLET_IP "docker load < /tmp/${APP_NAME}.tar.gz && rm /tmp/${APP_NAME}.tar.gz"
    
    # Clean up local tar file
    rm ${APP_NAME}.tar.gz
    
    log_success "Docker image transferred successfully"
}

# Transfer configuration files
transfer_configs() {
    log_info "Transferring configuration files..."
    
    # Create remote directory
    ssh $DROPLET_USER@$DROPLET_IP "mkdir -p /opt/sanad"
    
    # Transfer Docker Compose file
    scp $COMPOSE_FILE $DROPLET_USER@$DROPLET_IP:/opt/sanad/
    
    # Transfer environment file
    scp .env.droplet $DROPLET_USER@$DROPLET_IP:/opt/sanad/.env
    
    # Create necessary directories
    ssh $DROPLET_USER@$DROPLET_IP "mkdir -p /opt/sanad/storage /opt/sanad/logs /opt/sanad/traefik"
    
    log_success "Configuration files transferred successfully"
}

# Deploy application
deploy_application() {
    log_info "Deploying application on droplet..."
    
    # SSH to droplet and deploy
    ssh $DROPLET_USER@$DROPLET_IP << 'EOF'
        cd /opt/sanad
        
        # Stop existing services if running
        if [ -f docker-compose.yml ]; then
            docker-compose down
        fi
        
        # Start unified stack
        docker-compose -f docker-compose.unified.yml up -d
        
        # Wait for services to start
        sleep 30
        
        # Check service status
        docker-compose -f docker-compose.unified.yml ps
EOF
    
    log_success "Application deployed successfully"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Wait for services to be ready
    sleep 60
    
    # Test health endpoints
    log_info "Testing health endpoints..."
    
    # Test Appwrite
    if curl -f -s http://appwrite.sanad.kanousai.com/health > /dev/null; then
        log_success "Appwrite health check passed"
    else
        log_warning "Appwrite health check failed"
    fi
    
    # Test Node.js app (internal)
    ssh $DROPLET_USER@$DROPLET_IP "curl -f -s http://localhost:3000/api/v1/health" > /dev/null
    if [ $? -eq 0 ]; then
        log_success "Node.js application health check passed"
    else
        log_warning "Node.js application health check failed"
    fi
    
    # Show running containers
    log_info "Running containers:"
    ssh $DROPLET_USER@$DROPLET_IP "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"
}

# Main deployment process
main() {
    log_info "Starting unified droplet deployment..."
    
    check_prerequisites
    build_image
    transfer_image
    transfer_configs
    deploy_application
    verify_deployment
    
    log_success "Deployment completed successfully!"
    log_info "Next steps:"
    echo "1. Configure DNS for api.sanad.kanousai.com"
    echo "2. Test SSL certificate generation"
    echo "3. Verify all API endpoints"
    echo "4. Set up monitoring and backups"
}

# Run main function
main "$@"
