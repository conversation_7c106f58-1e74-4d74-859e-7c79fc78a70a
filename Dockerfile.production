# Production Dockerfile for PAIM Node.js Application
# Optimized for single droplet deployment

# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S paim -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && \
    npm cache clean --force && \
    rm -rf /tmp/*

# Copy built application from builder stage
COPY --from=builder --chown=paim:nodejs /app/dist ./dist
COPY --from=builder --chown=paim:nodejs /app/node_modules ./node_modules

# Create necessary directories
RUN mkdir -p /app/storage /app/logs && \
    chown -R paim:nodejs /app/storage /app/logs

# Copy additional files
COPY --chown=paim:nodejs scripts ./scripts
COPY --chown=paim:nodejs .env.example ./.env.example

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV USER=paim

# Expose port
EXPOSE 3000

# Switch to non-root user
USER paim

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/api/v1/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/main.js"]
