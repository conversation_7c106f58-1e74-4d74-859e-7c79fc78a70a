version: '3.8'

# Unified Docker Compose for Single Droplet Deployment
# Combines Appwrite services with PAIM Node.js application

services:
  # Traefik reverse proxy and load balancer
  traefik:
    image: traefik:v2.10
    container_name: sanad-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik
      - appwrite-certificates:/certificates
    networks:
      - appwrite
      - sanad-network
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.dns.acme.email=<EMAIL>
      - --certificatesresolvers.dns.acme.storage=/certificates/acme.json
      - --certificatesresolvers.dns.acme.httpchallenge.entrypoint=web

  # PAIM Node.js Application
  sanad-app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: sanad-paim-app
    restart: unless-stopped
    networks:
      - appwrite
      - sanad-network
    environment:
      # Application Configuration
      - NODE_ENV=production
      - PORT=3000
      
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=gpt-4
      - OPENAI_MAX_TOKENS=1000
      - OPENAI_TEMPERATURE=0.7
      
      # Twilio WhatsApp Configuration
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_WHATSAPP_NUMBER=${TWILIO_WHATSAPP_NUMBER}
      - TWILIO_WEBHOOK_URL=https://api.sanad.kanousai.com/api/v1/webhook/whatsapp
      
      # Security Configuration
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      
      # Storage Configuration
      - STORAGE_PROVIDER=local
      - STORAGE_PATH=/app/storage
      - MAX_FILE_SIZE=********
      
      # Email & Registration Configuration
      - EMAIL_SERVICE_PROVIDER=appwrite
      - FROM_EMAIL=${FROM_EMAIL}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - REGISTRATION_ENABLED=true
      - REQUIRE_EMAIL_CONFIRMATION=true
      - AUTO_APPROVE_WHITELIST=false
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      
      # Rate Limiting Configuration
      - THROTTLE_TTL=60
      - THROTTLE_LIMIT=10
      
      # CORS Configuration
      - CORS_ORIGIN=*
      
      # Logging Configuration
      - LOG_LEVEL=info
      - DETAILED_ERRORS=false
      
      # Health Check Configuration
      - HEALTH_CHECK_ENABLED=true
      - HEALTH_CHECK_TIMEOUT=5000
      
      # Feature Flags
      - FEATURE_VOICE_ENABLED=true
      - FEATURE_MEDIA_ENABLED=true
      - FEATURE_REMINDERS_ENABLED=true
      - FEATURE_ANALYTICS_ENABLED=true
      
      # Appwrite Configuration (Internal Network)
      - APPWRITE_ENDPOINT=http://appwrite/v1
      - APPWRITE_PROJECT_ID=68613111000daf6c1e1f
      - APPWRITE_API_KEY=standard_85ba900e4f4afe4a1e4b89d23222addd4fe187b22cb7527e473be03d39ed6b32ddd49eea4fb4596da8f8962376534ec15078d6933732b5acef07c5360d4e99a6cb006f85830182a999981892fd90fe9022cceeb62d37702ad7d7b3b47100ff4a4c4ede3846cd972fdc263de8009bd0e019fea5bcc2fc958621f8dec77f892ff1
      - APPWRITE_DATABASE_ID=68613111000daf6c1e1f
      - APPWRITE_STORAGE_BUCKET_ID=${APPWRITE_STORAGE_BUCKET_ID}
      
      # Appwrite Collection IDs
      - APPWRITE_USERS_COLLECTION_ID=${APPWRITE_USERS_COLLECTION_ID}
      - APPWRITE_REGISTRATIONS_COLLECTION_ID=${APPWRITE_REGISTRATIONS_COLLECTION_ID}
      - APPWRITE_WHITELIST_COLLECTION_ID=${APPWRITE_WHITELIST_COLLECTION_ID}
      - APPWRITE_SESSIONS_COLLECTION_ID=${APPWRITE_SESSIONS_COLLECTION_ID}
      - APPWRITE_MEMORIES_COLLECTION_ID=${APPWRITE_MEMORIES_COLLECTION_ID}
      - APPWRITE_REMINDERS_COLLECTION_ID=${APPWRITE_REMINDERS_COLLECTION_ID}
      - APPWRITE_CONVERSATIONS_COLLECTION_ID=${APPWRITE_CONVERSATIONS_COLLECTION_ID}
      - APPWRITE_EMAILS_COLLECTION_ID=${APPWRITE_EMAILS_COLLECTION_ID}
      - APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID=${APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID}
      
      # Redis Configuration (Internal Network)
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      - appwrite
      - redis
      - mariadb
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    labels:
      - "traefik.enable=true"
      - "traefik.constraint-label-stack=sanad"
      - "traefik.http.routers.sanad_api.rule=Host(`api.sanad.kanousai.com`) || Host(`sanad.kanousai.com`)"
      - "traefik.http.routers.sanad_api.service=sanad_api"
      - "traefik.http.routers.sanad_api.tls=true"
      - "traefik.http.routers.sanad_api.tls.certresolver=dns"
      - "traefik.http.services.sanad_api.loadbalancer.server.port=3000"
      - "traefik.http.routers.sanad_api.middlewares=sanad-headers"
      - "traefik.http.middlewares.sanad-headers.headers.customrequestheaders.X-Forwarded-Proto=https"

  # Appwrite main service
  appwrite:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite
    restart: unless-stopped
    networks:
      - appwrite
    labels:
      - "traefik.enable=true"
      - "traefik.constraint-label-stack=appwrite"
      - "traefik.http.routers.appwrite_api.rule=Host(`appwrite.sanad.kanousai.com`)"
      - "traefik.http.routers.appwrite_api.service=appwrite_api"
      - "traefik.http.routers.appwrite_api.tls=true"
      - "traefik.http.routers.appwrite_api.tls.certresolver=dns"
      - "traefik.http.services.appwrite_api.loadbalancer.server.port=80"
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - mariadb
      - redis
    environment:
      - _APP_ENV=production
      - _APP_WORKER_PER_CORE=6
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_ROUTER_PROTECTION=disabled
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_DOMAIN=appwrite.sanad.kanousai.com
      - _APP_DOMAIN_TARGET=appwrite.sanad.kanousai.com
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER=
      - _APP_REDIS_PASS=${REDIS_PASSWORD}
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_USAGE_STATS=enabled

  # MariaDB database
  mariadb:
    image: mariadb:10.7
    container_name: sanad-appwrite-mariadb
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-mariadb:/var/lib/mysql:rw
    environment:
      - MYSQL_ROOT_PASSWORD=${_APP_DB_ROOT_PASS}
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=${_APP_DB_USER}
      - MYSQL_PASSWORD=${_APP_DB_PASS}
    command: 'mysqld --innodb-flush-method=fsync'
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Redis cache
  redis:
    image: redis:7.0-alpine
    container_name: sanad-appwrite-redis
    restart: unless-stopped
    networks:
      - appwrite
      - sanad-network
    volumes:
      - appwrite-redis:/data:rw
    command: redis-server --save 60 1 --loglevel warning --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

networks:
  appwrite:
    name: appwrite
  sanad-network:
    name: sanad-network

volumes:
  appwrite-mariadb:
  appwrite-redis:
  appwrite-cache:
  appwrite-uploads:
  appwrite-certificates:
  appwrite-functions:
  appwrite-config:
