# Simple single-stage build for Digital Ocean App Platform
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV=production

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies first
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove dev dependencies after build
RUN npm ci --only=production && npm cache clean --force

# Create necessary directories with proper permissions
RUN mkdir -p /tmp/storage logs && chmod 755 /tmp/storage logs

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "dist/main.js"]
