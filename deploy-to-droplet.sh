#!/bin/bash

# Unified Droplet Deployment Script
# Execute this script on the droplet to deploy the PAIM application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
APP_NAME="sanad-paim"
DEPLOY_DIR="/opt/sanad"
COMPOSE_FILE="docker-compose.unified.yml"

log_info "Starting PAIM unified deployment on droplet..."

# Create deployment directory
log_info "Creating deployment directory..."
mkdir -p $DEPLOY_DIR/storage $DEPLOY_DIR/logs $DEPLOY_DIR/traefik
cd $DEPLOY_DIR

# Create environment file with actual values
log_info "Creating environment configuration..."
cat > .env << 'EOF'
# Environment Configuration for Single Droplet Deployment
# Production environment based on current .env file

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=94c69abeac98621da9b803a15893ea2c
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Security Configuration
JWT_SECRET=7f3e9c4a8d2b1f6e5c9a3d7b2e8f4c1a9e6d3b7f2c5a8d1e4b9c6f3a7d2e5b8c1f4a9e6d3b7
ENCRYPTION_KEY=9a2c7f1e4b8d5c3a6f9e2d7b4c8a1f5e3d6b9c2a7f4e1d8b5c3a9f6e2d7b4c1a8f5e3d6b9c2

# Email Configuration
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ADMIN_API_KEY=admin-key-change-me

# Appwrite Configuration
APPWRITE_PROJECT_ID=68613111000daf6c1e1f
APPWRITE_API_KEY=standard_85ba900e4f4afe4a1e4b89d23222addd4fe187b22cb7527e473be03d39ed6b32ddd49eea4fb4596da8f8962376534ec15078d6933732b5acef07c5360d4e99a6cb006f85830182a999981892fd90fe9022cceeb62d37702ad7d7b3b47100ff4a4c4ede3846cd972fdc263de8009bd0e019fea5bcc2fc958621f8dec77f892ff1
APPWRITE_DATABASE_ID=68613111000daf6c1e1f
APPWRITE_STORAGE_BUCKET_ID=default

# Appwrite Collection IDs
APPWRITE_USERS_COLLECTION_ID=users
APPWRITE_REGISTRATIONS_COLLECTION_ID=registrations
APPWRITE_WHITELIST_COLLECTION_ID=whitelist
APPWRITE_SESSIONS_COLLECTION_ID=sessions
APPWRITE_MEMORIES_COLLECTION_ID=memories
APPWRITE_REMINDERS_COLLECTION_ID=reminders
APPWRITE_CONVERSATIONS_COLLECTION_ID=conversations
APPWRITE_EMAILS_COLLECTION_ID=emails
APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID=email_confirmations

# Database Configuration
_APP_DB_USER=appwrite
_APP_DB_PASS=secure_db_password_2024
_APP_DB_ROOT_PASS=secure_root_password_2024

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_2024

# Appwrite Internal Configuration
_APP_OPENSSL_KEY_V1=your_openssl_key_here_32_chars_min

# Domain Configuration
_APP_DOMAIN_TARGET=sanad.kanousai.com
EOF

# Check if Docker is installed and running
log_info "Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed. Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl start docker
    systemctl enable docker
fi

if ! command -v docker-compose &> /dev/null; then
    log_info "Installing Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# Stop existing services
log_info "Stopping existing services..."
docker-compose down 2>/dev/null || true

# Pull the Node.js image (we'll build it locally since we can't transfer)
log_info "Building Node.js application image..."

# Create a simple Dockerfile for the Node.js app
cat > Dockerfile << 'EOF'
FROM node:18-alpine

# Install runtime dependencies
RUN apk add --no-cache dumb-init curl ca-certificates

# Create app user
RUN addgroup -g 1001 -S nodejs && adduser -S paim -u 1001 -G nodejs

WORKDIR /app

# Create a minimal Node.js app for now (placeholder)
RUN echo '{"name":"sanad-paim","version":"1.0.0","main":"server.js","scripts":{"start":"node server.js"}}' > package.json

# Create a simple server
RUN echo 'const express = require("express"); const app = express(); app.get("/api/v1/health", (req, res) => res.json({status: "ok", timestamp: new Date().toISOString()})); app.listen(3000, () => console.log("Server running on port 3000"));' > server.js

# Install express
RUN npm install express

# Switch to non-root user
USER paim

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/api/v1/health || exit 1

CMD ["dumb-init", "node", "server.js"]
EOF

# Build the image
docker build -t sanad-paim:latest .

log_success "Node.js application image built successfully"

# Start the unified stack
log_info "Starting unified stack..."
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to start
log_info "Waiting for services to start..."
sleep 60

# Check service status
log_info "Checking service status..."
docker-compose -f $COMPOSE_FILE ps

# Test health endpoints
log_info "Testing health endpoints..."
sleep 30

# Test Node.js app
if curl -f -s http://localhost:3000/api/v1/health > /dev/null; then
    log_success "Node.js application health check passed"
else
    log_warning "Node.js application health check failed"
fi

# Show running containers
log_info "Running containers:"
docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'

# Show logs
log_info "Recent logs:"
docker-compose -f $COMPOSE_FILE logs --tail=20

log_success "Deployment completed!"
log_info "Next steps:"
echo "1. Configure DNS for api.sanad.kanousai.com -> $(curl -s ifconfig.me)"
echo "2. Test SSL certificate generation"
echo "3. Verify all API endpoints"
echo "4. Upload your actual application code"
EOF
